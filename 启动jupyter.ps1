# PowerShell脚本启动Jupyter
Write-Host "正在启动Jupyter Lab..." -ForegroundColor Green
Write-Host "使用虚拟环境: venv" -ForegroundColor Yellow

# 设置工作目录
Set-Location $PSScriptRoot

# 检查虚拟环境是否存在
if (Test-Path "venv\Scripts\python.exe") {
    Write-Host "找到虚拟环境，正在启动Jupyter Lab..." -ForegroundColor Green
    & "venv\Scripts\jupyter.exe" lab
} else {
    Write-Host "错误: 找不到虚拟环境" -ForegroundColor Red
    Write-Host "请确保venv文件夹存在" -ForegroundColor Red
}

Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
