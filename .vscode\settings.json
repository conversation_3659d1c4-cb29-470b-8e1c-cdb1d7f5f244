{"python.defaultInterpreterPath": "./venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "python.envFile": "${workspaceFolder}/.env", "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "files.associations": {"*.py": "python"}, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}"}, "jupyter.defaultKernel": "venv", "jupyter.kernels.excludePythonEnvironments": ["./venv/Scripts/python.exe"]}